"use client"

import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { motion } from "framer-motion"
import { useTranslations } from "next-intl"
import { Factory, Users, Award, Globe } from "lucide-react"

export default function AboutPage() {
  const t = useTranslations("about")

  const companyStats = [
    { icon: Factory, label: t("company.founded"), value: t("company.founded") },
    { icon: Globe, label: t("company.location"), value: t("company.location") },
    { icon: Users, label: t("company.employees"), value: t("company.employees") },
    { icon: Award, label: t("company.production"), value: t("company.production") },
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-[80vh] bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-32 h-32 border border-white rounded-full"></div>
          <div className="absolute top-40 right-32 w-24 h-24 border border-white rounded-full"></div>
          <div className="absolute bottom-32 left-40 w-16 h-16 border border-white rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center text-white">
            <motion.div
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="font-heading text-5xl md:text-7xl font-bold mb-6">
                {t("hero.title")}
              </h1>
              <p className="text-2xl md:text-3xl mb-4 bg-gradient-to-r from-[#ff6b00] to-[#009a9f] bg-clip-text text-transparent font-semibold">
                {t("hero.subtitle")}
              </p>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                {t("hero.description")}
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ x: -100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="font-heading text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                {t("company.title")}
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-8">
                {t("company.description")}
              </p>
              
              {/* Company Stats */}
              <div className="grid grid-cols-2 gap-6">
                {companyStats.map((stat, index) => (
                  <motion.div
                    key={index}
                    className="text-center p-4 bg-gray-50 rounded-lg"
                    initial={{ y: 30, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <stat.icon className="w-8 h-8 mx-auto mb-2 text-[#ff6b00]" />
                    <p className="text-sm text-gray-600 mb-1">{stat.label}</p>
                    <p className="font-semibold text-gray-900">{stat.value}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ x: 100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative bg-gradient-to-br from-[#ff6b00]/10 to-[#009a9f]/10 rounded-3xl p-8">
                <Image
                  src="/images/evo-logo-h.png"
                  alt="EVO Industries"
                  width={400}
                  height={200}
                  className="w-full h-auto object-contain mb-6"
                />
                <div className="text-center">
                  <Image
                    src="/images/evo-logo.png"
                    alt="EVO Industries Logo"
                    width={120}
                    height={120}
                    className="mx-auto"
                  />
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Values */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="font-heading text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {t("mission.title")}
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              {t("mission.description")}
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {t.raw("mission.values").map((value: any, index: number) => (
              <motion.div
                key={index}
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full text-center hover:shadow-lg transition-shadow">
                  <CardContent className="p-8">
                    <div className="text-4xl mb-4">{value.icon}</div>
                    <h3 className="font-heading text-xl font-bold text-gray-900 mb-3">
                      {value.title}
                    </h3>
                    <p className="text-gray-600">{value.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technology Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ x: -100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="font-heading text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                {t("technology.title")}
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-8">
                {t("technology.description")}
              </p>
              
              <div className="space-y-4">
                {t.raw("technology.features").map((feature: string, index: number) => (
                  <motion.div
                    key={index}
                    className="flex items-center space-x-4"
                    initial={{ x: -20, opacity: 0 }}
                    whileInView={{ x: 0, opacity: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className={`w-8 h-8 ${index % 2 === 0 ? 'bg-[#ff6b00]' : 'bg-[#009a9f]'} rounded-full flex items-center justify-center`}>
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span className="text-gray-700 text-lg">{feature}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ x: 100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl p-8">
                <Image
                  src="/images/hero-aero3.svg"
                  alt="Aero 3 Technology"
                  width={400}
                  height={300}
                  className="w-full h-auto object-contain"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Commitment Section */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="font-heading text-4xl md:text-5xl font-bold mb-6">
              {t("commitment.title")}
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              {t("commitment.description")}
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {t.raw("commitment.pillars").map((pillar: any, index: number) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <div className={`w-16 h-16 ${index === 0 ? 'bg-[#ff6b00]' : index === 1 ? 'bg-[#009a9f]' : 'bg-purple-500'} rounded-full flex items-center justify-center mx-auto mb-6`}>
                  <span className="text-white text-2xl font-bold">{index + 1}</span>
                </div>
                <h3 className="font-heading text-xl font-bold mb-4">{pillar.title}</h3>
                <p className="text-gray-300">{pillar.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team & CTA Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="font-heading text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {t("team.title")}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
              {t("team.description")}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-[#ff6b00] hover:bg-[#e55a00] text-white px-8 py-4 text-lg font-semibold"
              >
                <Link href="/contact">{t("team.cta")}</Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white px-8 py-4 text-lg font-semibold"
              >
                <Link href="/contact">Nous contacter</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
