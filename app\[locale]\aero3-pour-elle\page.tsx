"use client"

import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import ProductHighlights from "@/components/ProductHighlights"
import { motion } from "framer-motion"
import { useTranslations } from "next-intl"

export default function Aero3PourEllePage() {
  const t = useTranslations("aero3ForHer")

  const highlights = t.raw("highlights.items")

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-[70vh] bg-gradient-to-br from-[#009a9f] to-[#e02e8b] flex items-center">
        <div className="container mx-auto px-4 grid md:grid-cols-2 gap-12 items-center">
          <div className="text-white">
            <h1 className="font-heading text-5xl md:text-7xl mb-6">{t("hero.title")}</h1>
            <p className="text-xl mb-8 opacity-90">
              {t("hero.description")}
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-white text-[#009a9f] hover:bg-gray-100">
                <Link href="/contact">{t("hero.whereToBuy")}</Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-[#009a9f]"
              >
                <Link href="/technologie">{t("hero.seeTechnology")}</Link>
              </Button>
            </div>
          </div>
          <div className="relative">
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.8 }}
            >
              <Image
                src="/images/aero3-pour-elle-razor.png"
                alt="Aero 3 Pour Elle"
                width={250}
                height={350}
                className="mx-auto max-w-xs md:max-w-sm max-h-[350px] h-auto object-contain drop-shadow-2xl rotate-45"
                priority
              />
            </motion.div>
            
          </div>
        </div>
      </section>

      {/* Product Highlights */}
      <ProductHighlights title={t("highlights.title")} bullets={highlights} colors="femme" />

      {/* Specifications */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="font-heading text-4xl text-center mb-12">{t("specifications.title")}</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {t.raw("specifications.items").map((spec: any, index: number) => (
              <Card key={index}>
                <CardContent className="p-6 text-center">
                  <h3 className="font-heading text-xl mb-4 color-femme-primary">{spec.title}</h3>
                  <p className="text-gray-600">{spec.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Beauty & Care Section */}
      <section className="py-5 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ x: -100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="font-heading text-4xl mb-6">
                <span className="color-femme-accent">{t("beautyCare.title").split(" & ")[0]}</span> & <span className="color-femme-primary">{t("beautyCare.title").split(" & ")[1]}</span>
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                {t("beautyCare.description")}
              </p>
              <div className="space-y-4">
                {t.raw("beautyCare.features").map((feature: string, index: number) => (
                  <div key={index} className="flex items-center space-x-4">
                    <div className={`w-8 h-8 ${index % 2 === 0 ? 'bg-[#e02e8b]' : 'bg-[#009a9f]'} rounded-full flex items-center justify-center`}>
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ x: 100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative bg-gradient-to-br from-[#e02e8b]/10 to-[#009a9f]/10 rounded-3xl p-2 lg:p-4">
                <Image
                  src="/images/aero3-pour-elle-razor.png"
                  alt="Aero 3 Pour Elle - Détail"
                  width={180}
                  height={200}
                  className="mx-auto max-w-full h-auto object-contain drop-shadow-2xl"
                />

                {/* Floating feature callouts */}
                <motion.div
                  className="absolute top-8 left-4 bg-white rounded-lg shadow-lg p-3 max-w-32"
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut" }}
                >
                  <div className="text-xs font-semibold color-femme-accent">{t("beautyCare.callouts.softness.title")}</div>
                  <div className="text-xs text-gray-600">{t("beautyCare.callouts.softness.subtitle")}</div>
                </motion.div>

                <motion.div
                  className="absolute bottom-16 right-4 bg-white rounded-lg shadow-lg p-3 max-w-32"
                  animate={{ y: [0, 10, 0] }}
                  transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY, ease: "easeInOut", delay: 1.5 }}
                >
                  <div className="text-xs font-semibold color-femme-primary">{t("beautyCare.callouts.elegance.title")}</div>
                  <div className="text-xs text-gray-600">{t("beautyCare.callouts.elegance.subtitle")}</div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[#009a9f]">
        <div className="container mx-auto px-4 text-center">
          <h2 className="font-heading text-4xl text-white mb-6">{t("cta.title")}</h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            {t("cta.description")}
          </p>
          <Button size="lg" className="bg-white text-[#009a9f] hover:bg-gray-100">
            <Link href="/contact">{t("cta.button")}</Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
