import ContactForm from "@/components/ContactForm"
import { Card, CardContent } from "@/components/ui/card"
import { MapPin, Phone, Mail, Clock } from "lucide-react"
import { useTranslations } from "next-intl"

export default function ContactPage() {
  const t = useTranslations();
  const storeLocations = [
    {
      name: "Pharmacie Centrale",
      address: "Rue Didouche Mourad, Alger Centre",
      city: "Alger",
      phone: "+213 21 123 456",
      hours: "8h-20h",
    },
    {
      name: "Parapharmacie Santé Plus",
      address: "Boulevard de l'ANP, Oran",
      city: "Oran",
      phone: "+213 41 234 567",
      hours: "9h-19h",
    },
    {
      name: "Supermarché Monoprix",
      address: "Centre Commercial Bab Ezzouar",
      city: "Alger",
      phone: "+213 21 345 678",
      hours: "9h-22h",
    },
    {
      name: "Pharmacie du Centre",
      address: "Place 1er Novembre, Constantine",
      city: "Constantine",
      phone: "+213 31 456 789",
      hours: "8h-19h",
    },
  ]

  return (
    <div className="min-h-screen py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h1 className="font-heading text-5xl mb-6">{t('contact.title')}</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('contact.desc')}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div>
            <h2 className="font-heading text-3xl mb-6">{t('contact.sendMessage')}</h2>
            <ContactForm />
          </div>

          {/* Contact Info */}
          <div>
            <h2 className="font-heading text-3xl mb-6">{t('contact.ourInfo')}</h2>

            <div className="space-y-6 mb-8">
              <div className="flex items-start space-x-4">
                <MapPin className="w-6 h-6 color-homme-primary flex-shrink-0 mt-1" />
                <div>
                  <h3 className="font-semibold mb-1">{t('contact.address')}</h3>
                  <p className="text-gray-600">
                    {t('contact.address')}<br />
                    {t('contact.city')}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Phone className="w-6 h-6 color-femme-primary flex-shrink-0 mt-1" />
                <div>
                  <h3 className="font-semibold mb-1">{t('contact.phone')}</h3>
                  <p className="text-gray-600">{t('contact.phone')}</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Mail className="w-6 h-6 color-homme-accent flex-shrink-0 mt-1" />
                <div>
                  <h3 className="font-semibold mb-1">{t('contact.email')}</h3>
                  <p className="text-gray-600">{t('contact.email')}</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Clock className="w-6 h-6 color-femme-accent flex-shrink-0 mt-1" />
                <div>
                  <h3 className="font-semibold mb-1">{t('contact.hours')}</h3>
                  <p className="text-gray-600">
                    {t('contact.hoursDetails')}
                  </p>
                </div>
              </div>
            </div>

            {/* Map placeholder */}
            <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
              <p className="text-gray-500">{t('contact.map')}</p>
            </div>
          </div>
        </div>

        {/* Store Locations */}
        <section className="mt-20">
          <h2 className="font-heading text-3xl text-center mb-12">{t('contact.storesTitle')}</h2>
          <p className="text-center text-gray-600 mb-8">{t('contact.storesDesc')}</p>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {storeLocations.map((store, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <h3 className="font-heading text-lg mb-3">{store.name}</h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-start space-x-2">
                      <MapPin className="w-4 h-4 flex-shrink-0 mt-0.5" />
                      <div>
                        <p>{store.address}</p>
                        <p className="font-semibold">{store.city}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4 flex-shrink-0" />
                      <p>{store.phone}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 flex-shrink-0" />
                      <p>{store.hours}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Distributor CTA */}
        <section className="mt-20 bg-gradient-to-r from-[#ff6b00] to-[#009a9f] rounded-2xl p-8 text-center text-white">
          <h2 className="font-heading text-3xl mb-4">{t('contact.distributorTitle')}</h2>
          <p className="text-xl mb-6 opacity-90">
            {t('contact.distributorDesc')}
          </p>
          <a
            href="#contact-form"
            className="inline-flex items-center justify-center px-8 py-3 bg-white text-[#ff6b00] rounded-lg hover:bg-gray-100 transition-colors font-semibold"
          >
            {t('contact.distributorCta')}
          </a>
        </section>
      </div>
    </div>
  )
}
