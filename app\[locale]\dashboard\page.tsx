"use client"

import { useAuth } from "@/components/AuthProvider";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Image from "next/image";

const KPIS = [
  {
    title: "Taux de conversion client",
    value: "12,5%",
    color: "bg-orange-100",
    accent: "text-orange-600",
  },
  {
    title: "Coûts logistiques",
    value: "1 200 000 DA",
    color: "bg-cyan-100",
    accent: "text-cyan-700",
  },
  {
    title: "ROI par vague de production",
    value: "+18%",
    color: "bg-orange-50",
    accent: "text-orange-500",
  },
  {
    title: "Taux de réachat",
    value: "32%",
    color: "bg-cyan-50",
    accent: "text-cyan-600",
  },
  {
    title: "Fidélisation client",
    value: "78%",
    color: "bg-orange-100",
    accent: "text-orange-600",
  },
];

export default function DashboardPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [activeSection, setActiveSection] = useState("dashboard");

  useEffect(() => {
    if (!user) {
      router.replace("/login");
    }
  }, [user, router]);

  if (!user) return null;

  return (
    <div className="min-h-screen flex bg-gray-50">
      {/* Sidebar */}
      <aside className="w-64 bg-white shadow-lg flex flex-col p-6">
        <div className="flex items-center mb-10">
          <Image src="/images/aero3-logo.png" alt="Shift Aero 3" width={40} height={40} className="mr-2" />
          <span className="font-bold text-xl text-orange-600">Shift Aero 3</span>
        </div>
        <nav className="flex-1">
          <ul className="space-y-2">
            <li>
              <button
                className={`w-full text-left px-4 py-2 rounded-lg font-medium transition ${activeSection === "dashboard" ? "bg-orange-100 text-orange-700" : "hover:bg-orange-50 text-gray-700"}`}
                onClick={() => setActiveSection("dashboard")}
              >
                Tableau de bord
              </button>
            </li>
            <li>
              <button
                className={`w-full text-left px-4 py-2 rounded-lg font-medium transition ${activeSection === "ajouter" ? "bg-cyan-100 text-cyan-700" : "hover:bg-cyan-50 text-gray-700"}`}
                onClick={() => setActiveSection("ajouter")}
              >
                Ajouter données
              </button>
            </li>
            <li className="mt-8">
              <button
                className="w-full text-left px-4 py-2 rounded-lg font-medium bg-gray-100 text-gray-500 hover:bg-gray-200"
                onClick={logout}
              >
                Déconnexion
              </button>
            </li>
          </ul>
        </nav>
      </aside>
      {/* Main Content */}
      <main className="flex-1 p-10">
        {activeSection === "dashboard" && (
          <section>
            <h1 className="text-3xl font-bold mb-8 text-gray-900">Suivi de la performance</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {KPIS.map((kpi, idx) => (
                <div
                  key={kpi.title}
                  className={`rounded-xl shadow-md p-6 ${kpi.color} flex flex-col items-start`}
                >
                  <div className="flex items-center mb-2">
                    <span className={`font-semibold text-lg ${kpi.accent}`}>{kpi.title}</span>
                  </div>
                  <div className="text-3xl font-bold mb-2 text-gray-900">{kpi.value}</div>
                  {/* Mini chart placeholder */}
                  <div className="w-full h-10 bg-white rounded mt-2 flex items-center justify-center">
                    <span className="text-xs text-gray-300">[mini chart]</span>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}
        {activeSection === "ajouter" && (
          <section>
            <h2 className="text-2xl font-bold mb-6 text-cyan-700">Ajouter des données</h2>
            <form className="bg-white p-6 rounded-lg shadow-md max-w-md">
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Nom du KPI</label>
                <input className="w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-cyan-400" placeholder="Nom du KPI" />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Valeur</label>
                <input className="w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-cyan-400" placeholder="Valeur" />
              </div>
              <button type="submit" className="w-full bg-cyan-500 hover:bg-cyan-600 text-white font-semibold py-2 rounded transition">Ajouter</button>
            </form>
          </section>
        )}
      </main>
    </div>
  );
} 