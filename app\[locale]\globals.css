@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Neutres */
  --c-bg: #ffffff;
  --c-text: #1a1a1a;

  /* Segment Femme */
  --c-femme-primary: #009a9f;
  --c-femme-accent: #e02e8b;

  /* Segment Homme */
  --c-homme-primary: #ff6b00;
  --c-homme-accent: #101010;

  /* UI utility */
  --radius: 0.75rem;
  --transition: 250ms ease;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .font-heading {
    font-family: var(--font-poppins);
    font-weight: 600;
  }

  .font-body {
    font-family: var(--font-inter);
    font-weight: 400;
  }

  .color-femme-primary {
    color: var(--c-femme-primary);
  }

  .color-femme-accent {
    color: var(--c-femme-accent);
  }

  .color-homme-primary {
    color: var(--c-homme-primary);
  }

  .color-homme-accent {
    color: var(--c-homme-accent);
  }

  .bg-femme-primary {
    background-color: var(--c-femme-primary);
  }

  .bg-femme-accent {
    background-color: var(--c-femme-accent);
  }

  .bg-homme-primary {
    background-color: var(--c-homme-primary);
  }

  .bg-homme-accent {
    background-color: var(--c-homme-accent);
  }

  /* RTL Support for Arabic */
  .rtl {
    direction: rtl;
    text-align: right;
  }

  .rtl .space-x-4 > * + * {
    margin-right: 1rem;
    margin-left: 0;
  }

  .rtl .space-x-8 > * + * {
    margin-right: 2rem;
    margin-left: 0;
  }

  .rtl .mr-2 {
    margin-right: 0;
    margin-left: 0.5rem;
  }

  .rtl .mr-3 {
    margin-right: 0;
    margin-left: 0.75rem;
  }

  .rtl .ml-auto {
    margin-left: 0;
    margin-right: auto;
  }

  .rtl .text-left {
    text-align: right;
  }

  .rtl .text-right {
    text-align: left;
  }
}
