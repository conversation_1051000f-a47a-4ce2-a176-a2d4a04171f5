import type React from "react";
import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { AuthProvider } from "@/components/AuthProvider";
import { NextIntlClientProvider, hasLocale } from "next-intl";
import { notFound } from "next/navigation";
import { routing } from "@/i18n/routing";
import { Analytics } from "@vercel/analytics/next"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const poppins = Poppins({
  weight: ["400", "600"],
  subsets: ["latin"],
  variable: "--font-poppins",
});

export const metadata: Metadata = {
  title: "Shift Expert - Votre peau mérite l'excellence",
  description:
    "Découvrez les rasoirs Aero 3 fabriqués en Algérie. Qualité premium pour homme et femme.",
  generator: "KhelilSoft",
};

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }
  // Static import for messages

  return (
    <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'}>
      <body className={`${inter.variable} ${poppins.variable} font-sans ${locale === 'ar' ? 'rtl' : 'ltr'}`}>
        <AuthProvider>
          
          <NextIntlClientProvider>
          <Navbar />
            {children}
            <Footer />
          </NextIntlClientProvider>
          
        </AuthProvider>
        <Analytics/>
      </body>
    </html>
  );
}
