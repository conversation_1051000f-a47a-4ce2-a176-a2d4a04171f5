"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { useTranslations } from "next-intl"

export default function ContactForm() {
  const t = useTranslations();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "particulier",
    message: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 1000))

    setIsSubmitted(true)
    setIsSubmitting(false)
  }

  if (isSubmitted) {
    return (
      <Card className="bg-green-50 border-green-200">
        <CardContent className="p-6 text-center">
          <div className="text-green-600 text-4xl mb-4">✓</div>
          <h3 className="font-heading text-xl mb-2 text-green-800">{t('contactForm.sentTitle')}</h3>
          <p className="text-green-700">
            {t('contactForm.sentDesc')}
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card id="contact-form">
      <CardContent className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium mb-2">
                {t('contactForm.name')}
              </label>
              <Input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleChange}
                placeholder={t('contactForm.namePlaceholder')}
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-2">
                {t('contactForm.email')}
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                placeholder={t('contactForm.emailPlaceholder')}
              />
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium mb-2">
                {t('contactForm.phone')}
              </label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                placeholder={t('contactForm.phonePlaceholder')}
              />
            </div>

            <div>
              <label htmlFor="subject" className="block text-sm font-medium mb-2">
                {t('contactForm.subject')}
              </label>
              <select
                id="subject"
                name="subject"
                required
                value={formData.subject}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ff6b00] focus:border-transparent"
              >
                <option value="particulier">{t('contactForm.subjectOptions.particulier')}</option>
                <option value="distributeur">{t('contactForm.subjectOptions.distributeur')}</option>
                <option value="professionnel">{t('contactForm.subjectOptions.professionnel')}</option>
                <option value="presse">{t('contactForm.subjectOptions.presse')}</option>
                <option value="autre">{t('contactForm.subjectOptions.autre')}</option>
              </select>
            </div>
          </div>

          <div>
            <label htmlFor="message" className="block text-sm font-medium mb-2">
              {t('contactForm.message')}
            </label>
            <Textarea
              id="message"
              name="message"
              required
              rows={5}
              value={formData.message}
              onChange={handleChange}
              placeholder={t('contactForm.messagePlaceholder')}
            />
          </div>

          <div className="text-sm text-gray-600">
            <p>
              {t('contactForm.privacy')}
              <a href="/legal" className="color-homme-primary hover:underline">
                {/* The privacy policy link text is part of the privacy string */}
              </a>
              .
            </p>
          </div>

          <Button type="submit" className="w-full bg-[#ff6b00] hover:bg-[#e55a00]" disabled={isSubmitting}>
            {isSubmitting ? t('contactForm.sending') : t('contactForm.send')}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
