"use client"

import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { useTranslations } from "next-intl"

export default function ExcellenceSection() {
  const t = useTranslations();
  
  return (
    <section className="relative py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-black overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-40 h-40 border border-white rounded-full"></div>
        <div className="absolute top-40 right-32 w-32 h-32 border border-white rounded-full"></div>
        <div className="absolute bottom-32 left-40 w-24 h-24 border border-white rounded-full"></div>
        <div className="absolute bottom-20 right-20 w-16 h-16 border border-white rounded-full"></div>
      </div>

      {/* Animated Background Elements */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-2 h-2 bg-[#ff6b00] rounded-full"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.5, 1, 0.5],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute top-1/3 right-1/3 w-1.5 h-1.5 bg-[#009a9f] rounded-full"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [0.5, 1, 0.5],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
      <motion.div
        className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-white rounded-full"
        animate={{
          scale: [1, 2, 1],
          opacity: [0.3, 0.8, 0.3],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      />

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          
          {/* Main Message */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            <h2 className="font-heading text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6">
              Votre peau mérite{" "}
              <span className="bg-gradient-to-r from-[#ff6b00] to-[#009a9f] bg-clip-text text-transparent">
                l'excellence
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Découvrez la différence qu'apporte une technologie de rasage premium, 
              conçue avec passion en Algérie pour votre satisfaction quotidienne.
            </p>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            className="grid md:grid-cols-3 gap-8 mb-16"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="text-center">
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-[#ff6b00] to-[#e55a00] rounded-full flex items-center justify-center mx-auto mb-4"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl font-bold">3</span>
              </motion.div>
              <h3 className="font-heading text-xl text-white mb-2">Triple Lame</h3>
              <p className="text-gray-400">Précision ultime en un seul passage</p>
            </div>
            
            <div className="text-center">
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-[#009a9f] to-[#007a7f] rounded-full flex items-center justify-center mx-auto mb-4"
                whileHover={{ scale: 1.1, rotate: -5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">🇩🇿</span>
              </motion.div>
              <h3 className="font-heading text-xl text-white mb-2">Made in Algeria</h3>
              <p className="text-gray-400">Qualité et savoir-faire local</p>
            </div>
            
            <div className="text-center">
              <motion.div
                className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-white text-2xl">✨</span>
              </motion.div>
              <h3 className="font-heading text-xl text-white mb-2">Excellence</h3>
              <p className="text-gray-400">Innovation et performance</p>
            </div>
          </motion.div>

          {/* Call to Action */}
          <motion.div
            className="space-y-6"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-[#ff6b00] to-[#e55a00] hover:from-[#e55a00] hover:to-[#cc4f00] text-white px-8 py-4 text-lg font-semibold shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105"
              >
                <Link href="/contact" className="flex items-center space-x-2">
                  <span>Où acheter</span>
                  <motion.span
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    →
                  </motion.span>
                </Link>
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg font-semibold transition-all duration-300 transform hover:scale-105"
              >
                <Link href="/technologie">Découvrir la technologie</Link>
              </Button>
            </div>

            <p className="text-gray-400 text-sm">
              Rejoignez des milliers de clients satisfaits qui ont choisi l'excellence Aero 3
            </p>
          </motion.div>

          {/* Bottom Decorative Element */}
          <motion.div
            className="mt-16 flex justify-center"
            initial={{ scale: 0, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="relative">
              <motion.div
                className="w-32 h-32 border-2 border-[#ff6b00]/30 rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              />
              <motion.div
                className="absolute inset-4 w-24 h-24 border-2 border-[#009a9f]/30 rounded-full"
                animate={{ rotate: -360 }}
                transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <Image
                  src="/images/aero3-logo.png"
                  alt="Aero 3"
                  width={60}
                  height={30}
                  className="h-8 w-auto opacity-80"
                />
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent pointer-events-none" />
    </section>
  )
}
