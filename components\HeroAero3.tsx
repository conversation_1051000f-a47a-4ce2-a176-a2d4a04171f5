"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { useTranslations } from "next-intl"

export default function HeroAero3() {
  const t = useTranslations();
  
  return (
    <section className="relative w-full min-h-screen bg-gradient-to-br from-gray-100 via-gray-50 to-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-32 h-32 border border-gray-300 rounded-full"></div>
        <div className="absolute top-40 right-32 w-24 h-24 border border-gray-300 rounded-full"></div>
        <div className="absolute bottom-32 left-40 w-16 h-16 border border-gray-300 rounded-full"></div>
      </div>

      <div className="container mx-auto px-4 pt-10 h-screen flex items-center">
        <div className="grid lg:grid-cols-2 gap-12 items-center w-full">
          
          {/* Left Content */}
          <motion.div
            className="space-y-8 text-center lg:text-left"
            initial={{ x: -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {/* Aero3 Logo */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex justify-center lg:justify-start"
            >
              <Image
                src="/images/aero3-logo.png"
                alt="Aero 3"
                width={280}
                height={120}
                className="h-20 md:h-24 w-auto"
                priority
              />
            </motion.div>

            {/* Main Headline */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <h1 className="font-heading text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight">
                Construit plus{" "}
                <span className="text-[#ff6b00]">solidement</span>
                <br />
                pour durer plus{" "}
                <span className="text-[#009a9f]">longtemps</span>
              </h1>
            </motion.div>

            

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 1 }}
            >
              <Button 
                size="lg" 
                className="bg-[#ff6b00] hover:bg-[#e55a00] text-white px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Link href="/contact">Découvrir Aero 3</Link>
              </Button>
             
            </motion.div>
          </motion.div>

          {/* Right - Razor Visual */}
          <motion.div
            className="relative flex items-center justify-center"
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {/* Background Glow Effect */}
            <motion.div
              className="absolute w-96 h-96 bg-gradient-to-r from-[#ff6b00]/20 to-[#009a9f]/20 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: "linear"
              }}
            />

            {/* Surface Reflection */}
            <motion.div
              className="absolute bottom-0 w-80 h-4 bg-gradient-to-r from-transparent via-gray-300/30 to-transparent rounded-full blur-sm"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 1, delay: 1.2 }}
            />

            {/* Main Razor Image */}
            <motion.div
              className="relative z-10"
              initial={{ scale: 0.8, rotate: -10 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 1, delay: 0.6 }}
              whileHover={{ 
                scale: 1.05,
                rotate: 2,
                transition: { duration: 0.3 }
              }}
            >
              <Image
                src="/images/hero-aero3.svg"
                alt="Aero 3 - Tête de rasoir 3 lames"
                width={400}
                height={300}
                className="w-80 md:w-96 h-auto object-contain drop-shadow-2xl"
                priority
              />
              
              {/* Sparkle Effects */}
              <motion.div
                className="absolute top-8 left-8 w-2 h-2 bg-white rounded-full"
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: 0.5,
                }}
              />
              <motion.div
                className="absolute top-16 right-12 w-1.5 h-1.5 bg-white rounded-full"
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: 1.2,
                }}
              />
              <motion.div
                className="absolute bottom-20 left-16 w-1 h-1 bg-white rounded-full"
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: 1.8,
                }}
              />
            </motion.div>

            {/* Floating Elements */}
            <motion.div
              className="absolute top-20 right-20 w-8 h-8 border-2 border-[#ff6b00]/30 rounded-full"
              animate={{
                y: [-10, 10, -10],
                rotate: [0, 360],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-32 left-16 w-6 h-6 border-2 border-[#009a9f]/30 rounded-full"
              animate={{
                y: [10, -10, 10],
                rotate: [360, 0],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1.5 }}
      >
        <motion.div
          className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <motion.div
            className="w-1 h-3 bg-gray-400 rounded-full mt-2"
            animate={{ opacity: [1, 0, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </motion.div>
      </motion.div>
    </section>
  )
}
