"use client"

import { Swiper, SwiperSlide } from "swiper/react"
import { Autoplay, Pagination } from "swiper/modules"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { useTranslations } from "next-intl"

// Import Swiper styles
import "swiper/css"
import "swiper/css/pagination"

export default function HeroCarousel() {
  const t = useTranslations();
  return (
    <div className="relative w-full h-screen overflow-hidden">
      <Swiper
        modules={[Autoplay, Pagination]}
        autoplay={{
          delay: 6000,
          disableOnInteraction: false,
        }}
        loop={true}
        pagination={{
          clickable: true,
          bulletClass: "swiper-pagination-bullet !bg-white/50 !w-3 !h-3",
          bulletActiveClass: "swiper-pagination-bullet-active !bg-white",
        }}
        className="w-full h-full "
      >
        {/* Slide 1 - Aero 3 (Homme) */}
        <SwiperSlide>
          <div
            className="w-full h-auto min-h-[70vh] bg-[#fafafa] flex flex-col justify-start pt-20 md:h-full md:items-center md:justify-center"
            aria-label="Rasoir homme"
          >
            <div className="container mx-auto px-2 sm:px-4 h-full">
              <div className="flex flex-col md:flex-row items-center justify-center h-full gap-6 md:gap-12">
                {/* Text Block - Left */}
                <motion.div
                  className="flex-1 max-w-lg text-center md:text-left text-neutral-900 order-2 md:order-1"
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <p className="text-xs sm:text-sm md:text-base font-medium tracking-wider mb-2 sm:mb-4 text-neutral-500">
                    {t('carousel.collection')}
                  </p>
                  <h1 className="text-2xl sm:text-3xl md:text-6xl font-bold mb-4 sm:mb-6 leading-tight">{t('carousel.mainTitle')}</h1>
                  <p className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 text-neutral-700 leading-relaxed">
                    {t('carousel.mainDesc')}
                  </p>
                </motion.div>

                {/* Image - Right */}
                <motion.div
                  className="flex-1 flex justify-center items-center order-1 md:order-2"
                  initial={{ x: 100, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  <div className="relative flex items-center justify-center">
                    <div className="absolute w-24 h-24 sm:w-40 sm:h-40 md:w-56 md:h-56 rounded-full bg-black/5 blur-lg z-0" />
                    <Image
                      src="/images/aero3-expert-razor.png"
                      alt="Aero 3 - Rasoir 3 lames pour homme"
                      width={220}
                      height={330}
                      priority
                      className="relative z-10 w-36 sm:w-28 md:w-52 h-auto object-contain drop-shadow-xl transform md:rotate-45 mx-auto"
                    />
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </SwiperSlide>

        {/* Slide 2 - Aero 3 Pour Elle (Femme) */}
        <SwiperSlide>
          <div
            className="w-full h-auto min-h-[70vh] bg-[#fafafa] flex flex-col justify-start pt-24 md:h-full md:items-center md:justify-center"
            aria-label="Rasoir femme"
          >
            <div className="container mx-auto px-2 sm:px-4 h-full">
              <div className="flex flex-col md:flex-row items-center justify-center h-full gap-6 md:gap-12">
                {/* Image - Left */}
                <motion.div
                  className="flex-1 flex justify-center items-center order-1 md:order-1"
                  initial={{ x: -100, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  <div className="relative flex items-center justify-center">
                    <div className="absolute w-24 h-24 sm:w-40 sm:h-40 md:w-56 md:h-56 rounded-full bg-black/5 blur-lg z-0" />
                    <Image
                      src="/images/aero3-pour-elle-razor.png"
                      alt="Aero 3 Pour Elle - Rasoir 3 lames pour femme"
                      width={220}
                      height={330}
                      priority
                      className="relative z-10 w-36 sm:w-28 md:w-52 h-auto object-contain drop-shadow-xl transform md:-rotate-45 mx-auto"
                    />
                  </div>
                </motion.div>

                {/* Text Block - Right */}
                <motion.div
                  className="flex-1 max-w-lg text-center md:text-left text-neutral-900 order-2 md:order-2"
                  initial={{ y: 50, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <p className="text-xs sm:text-sm md:text-base font-medium tracking-wider mb-2 sm:mb-4 text-neutral-500">
                    {t('carousel.collectionForHer')}
                  </p>
                  <h1 className="text-2xl sm:text-3xl md:text-6xl font-bold mb-4 sm:mb-6 leading-tight">{t('carousel.forHerTitle')}</h1>
                  <p className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 text-neutral-700 leading-relaxed">
                    {t('carousel.forHerDesc')}
                  </p>
                  <Button
                    asChild
                    size="lg"
                    variant="outline"
                    className="border-neutral-800 text-neutral-900 hover:bg-neutral-100 hover:text-neutral-900 transition-all duration-300 bg-transparent"
                    aria-label={t('carousel.ariaAero3ForHer')}
                  >
                    <Link href="/aero3-pour-elle">{t('carousel.discoverAero3ForHer')}</Link>
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>
        </SwiperSlide>
      </Swiper>

      {/* Custom pagination styles */}
      <style jsx global>{`
        .swiper-pagination {
          bottom: 2rem !important;
        }
        .swiper-pagination-bullet {
          margin: 0 6px !important;
          transition: all 0.3s ease !important;
        }
        .swiper-pagination-bullet-active {
          transform: scale(1.2) !important;
        }
      `}</style>
    </div>
  )
}
