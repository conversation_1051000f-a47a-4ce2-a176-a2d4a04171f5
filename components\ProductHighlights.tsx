"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"

interface Highlight {
  title: string
  description: string
  icon: string
}

interface ProductHighlightsProps {
  title: string
  bullets: Highlight[]
  colors: "homme" | "femme"
}

export default function ProductHighlights({ title, bullets, colors }: ProductHighlightsProps) {
  const colorClasses = {
    homme: {
      primary: "color-homme-primary",
      bg: "bg-homme-primary",
      accent: "color-homme-accent",
    },
    femme: {
      primary: "color-femme-primary",
      bg: "bg-femme-primary",
      accent: "color-femme-accent",
    },
  }

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <motion.h2
          className="font-heading text-4xl text-center mb-12"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          {title}
        </motion.h2>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {bullets.map((bullet, index) => (
            <motion.div
              key={index}
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center">
                  <div
                    className={`w-16 h-16 ${colorClasses[colors].bg} rounded-full flex items-center justify-center mx-auto mb-4`}
                  >
                    <span className="text-white text-2xl">{bullet.icon}</span>
                  </div>
                  <h3 className={`font-heading text-xl mb-3 ${colorClasses[colors].primary}`}>{bullet.title}</h3>
                  <p className="text-gray-600">{bullet.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
