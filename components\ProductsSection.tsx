"use client"

import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"
import Image from "next/image"
import { useTranslations } from "next-intl"

export default function ProductsSection() {
  const t = useTranslations();
  
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="font-heading text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Nos Produits
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Dé<PERSON>uvrez notre gamme de rasoirs haute performance, conçus pour répondre aux besoins spécifiques de chacun.
          </p>
        </motion.div>

        {/* Products Grid */}
        <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          
          {/* Aero 3 - Pour Homme */}
          <motion.div
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Card className="h-full overflow-hidden hover:shadow-2xl transition-all duration-500 group">
              <CardContent className="p-0">
                {/* Product Image Section */}
                <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 p-8 lg:p-12 min-h-[400px] flex items-center justify-center">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-8 left-8 w-16 h-16 border border-gray-300 rounded-full"></div>
                    <div className="absolute bottom-8 right-8 w-12 h-12 border border-gray-300 rounded-full"></div>
                  </div>
                  
                  {/* Glow Effect */}
                  <motion.div
                    className="absolute w-64 h-64 bg-gradient-to-r from-[#ff6b00]/20 to-[#009a9f]/20 rounded-full blur-3xl"
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      duration: 15,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  />
                  
                  {/* Product Image */}
                  <motion.div
                    className="relative z-10"
                    whileHover={{ 
                      scale: 1.05,
                      rotate: 5,
                      transition: { duration: 0.3 }
                    }}
                  >
                    <Image
                      src="/images/aero3-expert-razor.png"
                      alt="Aero 3 - Rasoir 3 lames pour homme"
                      width={280}
                      height={350}
                      className="w-64 h-auto object-contain drop-shadow-2xl"
                    />
                  </motion.div>
                  
                  {/* Badge */}
                  <div className="absolute top-4 left-4 bg-[#ff6b00] text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Produit Phare
                  </div>
                </div>

                {/* Product Info Section */}
                <div className="p-8">
                  <div className="mb-6">
                    <h3 className="font-heading text-3xl font-bold text-gray-900 mb-3">
                      Aero 3
                    </h3>
                    <p className="text-lg text-gray-600 mb-4">
                      {t('carousel.mainDesc')}
                    </p>
                    <p className="text-sm text-[#ff6b00] font-semibold mb-6">
                      Pour Homme • Made in Algeria
                    </p>
                  </div>

                  {/* Key Features */}
                  <div className="space-y-3 mb-8">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#ff6b00] rounded-full"></div>
                      <span className="text-gray-700">Triple lame haute précision</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#009a9f] rounded-full"></div>
                      <span className="text-gray-700">Bande lubrifiante enrichie</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#ff6b00] rounded-full"></div>
                      <span className="text-gray-700">Manche ergonomique</span>
                    </div>
                  </div>

                  {/* CTA Button */}
                  <Button 
                    className="w-full bg-[#ff6b00] hover:bg-[#e55a00] text-white font-semibold py-3 transition-all duration-300 group-hover:shadow-lg"
                    size="lg"
                  >
                    <Link href="/aero3-pour-homme" className="flex items-center justify-center space-x-2">
                      <span>Découvrir Aero 3</span>
                      <motion.span
                        className="text-lg"
                        animate={{ x: [0, 5, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        →
                      </motion.span>
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Aero 3 Pour Elle - Shiva */}
          <motion.div
            initial={{ x: 100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Card className="h-full overflow-hidden hover:shadow-2xl transition-all duration-500 group">
              <CardContent className="p-0">
                {/* Product Image Section */}
                <div className="relative bg-gradient-to-br from-pink-50 to-purple-50 p-8 lg:p-12 min-h-[400px] flex items-center justify-center">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-8 right-8 w-16 h-16 border border-pink-300 rounded-full"></div>
                    <div className="absolute bottom-8 left-8 w-12 h-12 border border-purple-300 rounded-full"></div>
                  </div>
                  
                  {/* Glow Effect */}
                  <motion.div
                    className="absolute w-64 h-64 bg-gradient-to-r from-[#e02e8b]/20 to-[#009a9f]/20 rounded-full blur-3xl"
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [360, 180, 0],
                    }}
                    transition={{
                      duration: 15,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  />
                  
                  {/* Product Image */}
                  <motion.div
                    className="relative z-10"
                    whileHover={{ 
                      scale: 1.05,
                      rotate: -5,
                      transition: { duration: 0.3 }
                    }}
                  >
                    <Image
                      src="/images/aero3-pour-elle-razor.png"
                      alt="Aero 3 Pour Elle - Rasoir 3 lames pour femme"
                      width={280}
                      height={350}
                      className="w-64 h-auto object-contain drop-shadow-2xl"
                    />
                  </motion.div>
                  
                  {/* Badge */}
                  <div className="absolute top-4 left-4 bg-[#e02e8b] text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Élégance
                  </div>
                </div>

                {/* Product Info Section */}
                <div className="p-8">
                  <div className="mb-6">
                    <h3 className="font-heading text-3xl font-bold text-gray-900 mb-3">
                      Aero 3 Pour Elle
                    </h3>
                    <p className="text-lg text-gray-600 mb-4">
                      {t('carousel.forHerDesc')}
                    </p>
                    <p className="text-sm text-[#e02e8b] font-semibold mb-6">
                      Pour Femme • Made in Algeria
                    </p>
                  </div>

                  {/* Key Features */}
                  <div className="space-y-3 mb-8">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#e02e8b] rounded-full"></div>
                      <span className="text-gray-700">Douceur extrême</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#009a9f] rounded-full"></div>
                      <span className="text-gray-700">Protection optimale</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#e02e8b] rounded-full"></div>
                      <span className="text-gray-700">Design élégant</span>
                    </div>
                  </div>

                  {/* CTA Button */}
                  <Button 
                    className="w-full bg-[#e02e8b] hover:bg-[#c02570] text-white font-semibold py-3 transition-all duration-300 group-hover:shadow-lg"
                    size="lg"
                  >
                    <Link href="/aero3-pour-elle" className="flex items-center justify-center space-x-2">
                      <span>Découvrir Aero 3 Pour Elle</span>
                      <motion.span
                        className="text-lg"
                        animate={{ x: [0, 5, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        →
                      </motion.span>
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <p className="text-lg text-gray-600 mb-6">
            Besoin d'aide pour choisir le rasoir qui vous convient ?
          </p>
          <Button 
            variant="outline" 
            size="lg"
            className="border-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white px-8 py-3 font-semibold transition-all duration-300"
          >
            <Link href="/contact">Nous contacter</Link>
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
