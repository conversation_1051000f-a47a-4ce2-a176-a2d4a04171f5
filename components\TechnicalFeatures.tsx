"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { <PERSON>issors, Zap, Droplets, RotateCcw, Waves } from "lucide-react"
import VerticalFeatureNav from "@/components/VerticalFeatureNav"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel"
import React, { useRef, useState } from "react"
import type { CarouselApi } from "@/components/ui/carousel"
import { useTranslations } from "next-intl"

const features = [
  {
    id: 1,
    icon: Scissors,
    titleKey: "features.0.title",
    descKey: "features.0.description",
    image: "/images/features/feature1.png",
    altKey: "features.0.alt",
    navIcon: "/images/features/icons/ic_feature01_2x.webp",
  },
  {
    id: 2,
    icon: Zap,
    titleKey: "features.1.title",
    descKey: "features.1.description",
    image: "/images/features/feature2.png",
    altKey: "features.1.alt",
    navIcon: "/images/features/icons/ic_feature02_2x.webp",
  },
  {
    id: 3,
    icon: Droplets,
    titleKey: "features.2.title",
    desc<PERSON>ey: "features.2.description",
    image: "/images/features/feature3.png",
    altKey: "features.2.alt",
    navIcon: "/images/features/icons/ic_feature03_2x.webp",
  },
  {
    id: 4,
    icon: RotateCcw,
    titleKey: "features.3.title",
    descKey: "features.3.description",
    image: "/images/features/feature4.png",
    altKey: "features.3.alt",
    navIcon: "/images/features/icons/ic_feature04_2x.webp",
  },
  {
    id: 5,
    icon: Waves,
    titleKey: "features.4.title",
    descKey: "features.4.description",
    image: "/images/features/feature5.png",
    altKey: "features.4.alt",
    navIcon: "/images/features/icons/ic_feature06_2x.webp",
  },
]

export default function TechnicalFeatures() {
  const [selectedIdx, setSelectedIdx] = useState(0)
  const [api, setApi] = useState<CarouselApi | null>(null)
  const t = useTranslations("technicalFeatures")

  // Synchronise l'index sélectionné avec le carousel
  const onSelect = () => {
    if (!api) return
    setSelectedIdx(api.selectedScrollSnap())
  }

  // Met à jour l'index lors du changement de slide
  React.useEffect(() => {
    if (!api) return
    api.on("select", onSelect)
    return () => {
      api.off("select", onSelect)
    }
  }, [api])

  // Scroll vers le slide choisi via la nav
  const goTo = (idx: number) => {
    api?.scrollTo(idx)
  }

  // Scroll automatique toutes les 4 secondes
  React.useEffect(() => {
    if (!api) return
    const interval = setInterval(() => {
      const nextIdx = (selectedIdx + 1) % features.length
      api.scrollTo(nextIdx)
    }, 4000)
    return () => clearInterval(interval)
  }, [api, selectedIdx])

  return (
    <section id="section2" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{t('title')}</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('description')}
          </p>
        </motion.div>

        {/* Carousel Features */}
        <div className="w-full max-w-4xl mx-auto">
          <Carousel setApi={setApi} orientation="horizontal" opts={{ loop: false }}>
            <CarouselContent>
              {features.map((feature, index) => (
                <CarouselItem key={feature.id}>
                  <motion.div
                    className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16 min-h-[400px]"
                    initial={{ y: 80, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    {/* Content Block */}
                    <div className="flex-1 text-center lg:text-left">
                      <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t(feature.titleKey)}</h3>
                      <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-lg mx-auto lg:mx-0">
                        {t(feature.descKey)}
                      </p>
                      {/* Feature Number */}
                      <div className="mt-6 flex items-center justify-center lg:justify-start">
                        <span className="text-6xl font-bold text-gray-100 mr-2">{String(feature.id).padStart(2, "0")}</span>
                        <div className="w-12 h-0.5 bg-gray-200" />
                      </div>
                    </div>
                    {/* Image Block */}
                    <div className="flex-1 flex justify-center items-center">
                      <motion.div className="relative" whileHover={{ scale: 1.05 }} transition={{ duration: 0.3 }}>
                        <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl p-8 lg:p-12 shadow-xl">
                          <Image
                            src={feature.image || "/placeholder.svg"}
                            alt={t(feature.altKey)}
                            width={400}
                            height={600}
                            className="w-64 md:w-80 h-auto object-contain drop-shadow-2xl mx-auto"
                          />
                        </div>
                      </motion.div>
                    </div>
                  </motion.div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>

        {/* Navigation horizontale sous le carousel */}
        <div className="flex flex-row justify-center gap-4 mt-8">
          {features.map((feature, idx) => (
            <button
              key={feature.id}
              onClick={() => goTo(idx)}
              className={`focus:outline-none group bg-transparent p-0 rounded-full transition-colors duration-200
                ${selectedIdx === idx ? "ring-2 ring-pink-500 bg-pink-100" : "opacity-50 grayscale hover:opacity-100 hover:grayscale-0 hover:ring-2 hover:ring-pink-400"}
              `}
              aria-label={t(feature.altKey)}
            >
              <Image src={feature.navIcon} alt={t(feature.altKey)} width={36} height={36} className="object-contain" />
            </button>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-20"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <p className="text-lg text-gray-600 mb-6">{t('ctaText')}</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.a
              href="/aero3-pour-elle"
              className="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-[#e02e8b] to-[#c91f7a] text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {t('ctaButton')}
            </motion.a>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
