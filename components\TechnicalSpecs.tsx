"use client"

import { motion } from "framer-motion"
import { Shield, Droplets, Grip } from "lucide-react"

const TECHNICAL_FEATURES = [
  {
    icon: Shield,
    title: "Acier inoxydable triple affûtage",
    description:
      "Lames en acier inoxydable de grade médical, affûtées selon un processus en trois étapes pour une coupe parfaite et durable.",
  },
  {
    icon: Droplets,
    title: "Lubrification à l'aloe vera",
    description:
      "Bande lubrifiante enrichie à l'aloe vera et à la vitamine E pour apaiser la peau et réduire les irritations.",
  },
  {
    icon: Grip,
    title: "Manche anti-glisse",
    description: "Design ergonomique avec surface texturée pour une prise en main sûre, même avec les mains mouillées.",
  },
]

export default function TechnicalSpecs() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.h2
          className="font-heading text-4xl text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          Nos engagements techniques
        </motion.h2>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Technical Features */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {TECHNICAL_FEATURES.map((feature, index) => (
              <motion.div
                key={index}
                className="flex items-start space-x-4"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <div className="flex-shrink-0 w-12 h-12 bg-[#ff6b00] rounded-full flex items-center justify-center">
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-heading text-xl mb-2">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Technical Diagram */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-gray-50 rounded-2xl p-8">
              <svg viewBox="0 0 400 300" className="w-full h-auto">
                {/* Razor Handle */}
                <rect x="50" y="180" width="300" height="40" rx="20" fill="#ff6b00" />
                <text x="200" y="205" textAnchor="middle" className="text-sm font-semibold fill-white">
                  Manche anti-glisse
                </text>

                {/* Razor Head */}
                <rect x="320" y="120" width="60" height="80" rx="10" fill="#009a9f" />

                {/* Triple Blades */}
                <line x1="325" y1="130" x2="375" y2="130" stroke="#333" strokeWidth="2" />
                <line x1="325" y1="140" x2="375" y2="140" stroke="#333" strokeWidth="2" />
                <line x1="325" y1="150" x2="375" y2="150" stroke="#333" strokeWidth="2" />

                {/* Lubricating Strip */}
                <rect x="325" y="160" width="50" height="8" rx="4" fill="#4ade80" />

                {/* Labels */}
                <text x="350" y="115" textAnchor="middle" className="text-xs font-semibold fill-gray-700">
                  Tête pivotante
                </text>
                <text x="350" y="185" textAnchor="middle" className="text-xs font-semibold fill-gray-700">
                  Bande lubrifiante
                </text>
                <text x="280" y="125" textAnchor="middle" className="text-xs font-semibold fill-gray-700">
                  3 lames espacées
                </text>

                {/* Connecting Lines */}
                <line x1="350" y1="120" x2="350" y2="130" stroke="#666" strokeWidth="1" strokeDasharray="2,2" />
                <line x1="350" y1="175" x2="350" y2="165" stroke="#666" strokeWidth="1" strokeDasharray="2,2" />
                <line x1="320" y1="135" x2="290" y2="130" stroke="#666" strokeWidth="1" strokeDasharray="2,2" />
              </svg>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
