import Image from "next/image"
import { useEffect, useRef, useState } from "react"

const features = [
  { id: "featsection1", img: "/images/features/icons/ic_feature01_2x.webp", alt: "3lames" },
  { id: "featsection2", img: "/images/features/icons/ic_feature02_2x.webp", alt: "Feature 2" },
  { id: "featsection3", img: "/images/features/icons/ic_feature03_2x.webp", alt: "Feature 3" },
  { id: "featsection4", img: "/images/features/icons/ic_feature04_2x.webp", alt: "Feature 4" },
  { id: "featsection5", img: "/images/features/icons/ic_feature06_2x.webp", alt: "Feature 5" },
]

export default function VerticalFeatureNav() {
  const [visible, setVisible] = useState(false)
  const [active, setActive] = useState(true)
  const [activeSection, setActiveSection] = useState<string | null>(null)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Affichage conditionnel selon la visibilité de section2
  useEffect(() => {
    const section2 = document.getElementById("section2")
    if (!section2) return
    const observer = new window.IntersectionObserver(
      ([entry]) => {
        setVisible(entry.isIntersecting)
      },
      { threshold: 0.1 }
    )
    observer.observe(section2)
    return () => observer.disconnect()
  }, [])

  // Disparition après 3s d'inactivité de scroll, réapparition au scroll
  useEffect(() => {
    if (!visible) return
    const handleScroll = () => {
      setActive(true)
      if (timerRef.current) clearTimeout(timerRef.current)
      timerRef.current = setTimeout(() => setActive(false), 10000)
    }
    window.addEventListener("scroll", handleScroll)
    // Lancer le timer dès l'apparition
    handleScroll()
    return () => {
      window.removeEventListener("scroll", handleScroll)
      if (timerRef.current) clearTimeout(timerRef.current)
    }
  }, [visible])

  // Observer toutes les sections pour déterminer la section active
  useEffect(() => {
    const sectionIds = features.map(f => f.id)
    const sections = sectionIds.map(id => document.getElementById(id)).filter(Boolean) as HTMLElement[]
    if (sections.length === 0) return
    const observer = new window.IntersectionObserver(
      (entries) => {
        // Trouver la section la plus visible
        let maxRatio = 0
        let currentId: string | null = null
        entries.forEach(entry => {
          if (entry.isIntersecting && entry.intersectionRatio > maxRatio) {
            maxRatio = entry.intersectionRatio
            currentId = entry.target.id
          }
        })
        if (currentId) setActiveSection(currentId)
      },
      { threshold: [0.2, 0.5, 0.8] }
    )
    sections.forEach(section => observer.observe(section))
    return () => observer.disconnect()
  }, [])

  const handleClick = (id: string) => {
    const el = document.getElementById(id)
    if (el) {
      el.scrollIntoView({ behavior: "smooth", block: "start" })
    }
  }

  if (!visible || !active) return null

  return (
    <nav className="fixed top-1/4 left-6 z-50 flex flex-col gap-6 items-center">
      {features.map((f) => {
        const isActive = f.id === activeSection
        return (
          <button
            key={f.id}
            onClick={() => handleClick(f.id)}
            className={`focus:outline-none group bg-transparent p-0 rounded-full transition-colors duration-200
              ${isActive ? "ring-2 ring-pink-500 bg-pink-100" : "opacity-50 grayscale hover:opacity-100 hover:grayscale-0 hover:ring-2 hover:ring-pink-400"}
            `}
            aria-label={f.alt}
          >
            <Image src={f.img} alt={f.alt} width={36} height={36} className="object-contain" />
          </button>
        )
      })}
    </nav>
  )
} 